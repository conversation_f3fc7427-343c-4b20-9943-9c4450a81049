import React, { memo } from 'react';
import {
  Tooltip,
  Icon<PERSON>utton,
  Stack,
  Chip,
  Checkbox,
  CircularProgress
} from '@mui/material';
import SupportIcon from '../common/SupportIcon';
import DownloadIcon from '../common/DownloadIcon';
import { Task } from '../../types/job';
import { formatDate, getStatusDisplay, getFileTypeLabel } from '../../utils/jobUtils';

interface TaskRowProps {
  task: Task;
  isSelected: boolean;
  onToggleSelect: (taskId: number) => void;
  onDownloadFile: (taskId: number, result: any) => void;
  onDownloadTaskFiles: (taskId: number) => void;
  onOpenSupportDialog: (taskId: number, datasetName: string, jobId: string) => void; // Changed batchId to jobId
  downloadingFiles: Record<string, boolean>;
  jobId: string; // Changed batchId to jobId
  gridTemplateColumns: string; // Add grid template columns prop
}

const TaskRow: React.FC<TaskRowProps> = ({
  task,
  isSelected,
  onToggleSelect,
  onDownloadFile,
  onDownloadTaskFiles,
  onOpenSupportDialog,
  downloadingFiles,
  jobId, // Changed batchId to jobId
  gridTemplateColumns
}) => {
  const fileTypes = ['kml', 'csv', 'pdf'];

  return (
    <div
      style={{
        display: 'grid',
        gridTemplateColumns: gridTemplateColumns,
        gap: 0,
        alignItems: 'center',
        padding: '8px 0',
        borderBottom: '1px solid rgba(224, 224, 224, 0.5)',
        cursor: 'pointer'
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.backgroundColor = '#f5f5f5';
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.backgroundColor = 'transparent';
      }}
    >
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', padding: '0 8px' }}>
        <Checkbox
          checked={isSelected}
          onChange={() => onToggleSelect(task.id)}
          inputProps={{ 'aria-labelledby': `task-${task.id}` }}
          size="small"
        />
      </div>
      <div id={`task-${task.id}`} style={{ display: 'flex', alignItems: 'center', padding: '0 8px' }}>
        {task.id}
      </div>
      <div style={{ display: 'flex', alignItems: 'center', padding: '0 8px' }}>
        {task.dataset?.name || 'N/A'}
      </div>
      <div style={{ display: 'flex', alignItems: 'center', padding: '0 8px' }}>
        <Stack direction="row" spacing={1} alignItems="center">
          <Tooltip title={getStatusDisplay(task.status, true).tooltip || ''} arrow>
            <Chip
              label={getStatusDisplay(task.status, true).text}
              color={getStatusDisplay(task.status, true).color as 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' | undefined}
              size="small"
            />
          </Tooltip>
        </Stack>
      </div>
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', padding: '0 8px' }}>
        <Tooltip title="Request support for this task">
          <IconButton
            size="small"
            onClick={() => onOpenSupportDialog(task.id, task.dataset?.name || '', jobId)} // Changed batchId to jobId
            aria-label={`Request support for task ${task.id}`}
            sx={{
              color: 'text.secondary',
              '&:hover': {
                color: 'primary.main'
              },
              padding: '4px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            <SupportIcon width={24} height={24} />
          </IconButton>
        </Tooltip>
      </div>
      <div style={{ display: 'flex', alignItems: 'center', padding: '0 8px' }}>
        {formatDate(task.created_at)}
      </div>
      <div style={{ padding: '0 8px' }}></div>

      {fileTypes.map((type) => {
        const result = task.task_results?.find(r =>
          type === 'csv' ?
            r.file_type?.toLowerCase() === 'csv_pva' :
            r.file_type?.toLowerCase() === type
        );

        const fileId = result ? `${task.id}-${result.id}` : '';
        const isDownloading = fileId && downloadingFiles[fileId];

        return (
          <div key={type} style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', padding: '0 8px' }}>
            {result && (
              <Tooltip title={`Download ${getFileTypeLabel(type)}`}>
                <span>
                  <IconButton
                    size="medium"
                    onClick={() => onDownloadFile(task.id, result)}
                    disabled={!!isDownloading}
                    aria-label={`Download ${getFileTypeLabel(type)} for task ${task.id}`}
                    sx={{
                      color: 'text.secondary',
                      '&:hover': {
                        color: 'primary.main'
                      }
                    }}
                  >
                    {isDownloading ? (
                      <CircularProgress size={20} />
                    ) : (
                      <DownloadIcon fileType={type} isMulti={false} />
                    )}
                  </IconButton>
                </span>
              </Tooltip>
            )}
          </div>
        );
      })}

      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', padding: '0 8px' }}>
        {task.task_results && task.task_results.length > 0 && (
          <Tooltip title="Download all files for this task">
            <span>
              <IconButton
                size="medium"
                onClick={() => onDownloadTaskFiles(task.id)}
                disabled={!!downloadingFiles[`task-${task.id}`]}
                aria-label={`Download all files for task ${task.id}`}
                sx={{
                  color: 'text.secondary',
                  '&:hover': {
                    color: 'primary.main'
                  }
                }}
              >
                {downloadingFiles[`task-${task.id}`] ? (
                  <CircularProgress size={20} />
                ) : (
                  <DownloadIcon fileType="all" isMulti={false} />
                )}
              </IconButton>
            </span>
          </Tooltip>
        )}
      </div>
    </div>
  );
};

export default memo(TaskRow);
