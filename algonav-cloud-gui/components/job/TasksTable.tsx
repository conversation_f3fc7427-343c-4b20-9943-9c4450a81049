import React, { memo, useState, useEffect } from 'react';
import {
  Ty<PERSON>graphy,
  Button,
  Tooltip,
  IconButton,
  TableSortLabel,
  Checkbox,
  CircularProgress
} from '@mui/material';
import DownloadIcon from '../common/DownloadIcon';

import { Task } from '../../types/job';
import { getFileTypeLabel, hasFilesOfType } from '../../utils/jobUtils';
import TaskRow from './TaskRow';

import BulkActionsToolbar from './BulkActionsToolbar';
import { createMultiKMLbulkTask } from '@/services/jobService';
import { useBulkTaskStatus } from '@/lib/hooks/useBulkTaskStatus';

import { Job } from '../../types/job'; // Changed Batch to Job
import { COLUMN_WIDTHS } from './tableConstants';

interface TasksTableProps {
  tasks: Task[];
  orderBy: string;
  order: 'asc' | 'desc';
  selectedTaskIds: number[];
  downloadingFiles: Record<string, boolean>;
  onSort: (property: string) => void;
  onToggleTaskSelection: (taskId: number) => void;
  onToggleAllTasksSelection: () => void;
  onDownloadFile: (taskId: number, result: any) => void;
  onDownloadTaskFiles: (taskId: number) => void;
  onDownloadAllOfType: (type: string) => void;
  onDownloadAll: () => void;
  onOpenSupportDialog: (taskId: number, datasetName: string, jobId: string) => void; // Changed batchId to jobId
  jobId: string; // Changed batchId to jobId
  job: Job; // Changed batch to job
}

const TasksTable: React.FC<TasksTableProps> = ({
  tasks,
  orderBy,
  order,
  selectedTaskIds,
  downloadingFiles,
  onSort,
  onToggleTaskSelection,
  onToggleAllTasksSelection,
  onDownloadFile,
  onDownloadTaskFiles,
  onDownloadAllOfType,
  onDownloadAll,
  onOpenSupportDialog,
  jobId, // Changed batchId to jobId
  job // Changed batch to job
}) => {
  const fileTypes = ['kml', 'csv', 'pdf'];

  // State for bulk job management
  const [bulkTaskId, setbulkTaskId] = useState<string | null>(null);
  const { status: bulkTaskStatus, error: bulkTaskError, downloadResult } = useBulkTaskStatus(bulkTaskId);
  
  // Check if all visible tasks are selected
  const allSelected = tasks.length > 0 && selectedTaskIds.length === tasks.length;
  const someSelected = selectedTaskIds.length > 0 && selectedTaskIds.length < tasks.length;
  
  // Reset bulk job when selection changes and a job is complete or has error
  useEffect(() => {
    if ((bulkTaskStatus === 'complete' || bulkTaskStatus === 'error') && bulkTaskId) {
      setbulkTaskId(null);
    }
  }, [selectedTaskIds, bulkTaskStatus, bulkTaskId]);

  // Handle downloading all selected task files
  const handleDownloadSelected = () => {
    selectedTaskIds.forEach(taskId => {
      onDownloadTaskFiles(taskId);
    });
  };

  // Handle creating a multi-KML file from selected tasks
  const handleCreateMultiKML = async () => {
    if (selectedTaskIds.length === 0) return;
    
    try {
      // Reset any previous job
      if (bulkTaskStatus === 'complete' || bulkTaskStatus === 'error') {
        setbulkTaskId(null);
      }
      
      // Create a new bulk job
      const firstSelectedTask = tasks.find(task => task.id === selectedTaskIds[0]);
      const multiKmlName = firstSelectedTask?.dataset?.name?.replace(/[^a-zA-Z0-9_.-]/g, '_') || 'UnknownDataset';
      // Use template name from the first task in the job as jobName proxy
      const jobName = job.tasks?.[0]?.global_job_template?.name?.replace(/[^a-zA-Z0-9_.-]/g, '_') || 'UnknownJob'; // Changed batch.jobs to job.tasks
      const taskCount = selectedTaskIds.length;
      const outputFilename = `${multiKmlName}_${jobName}_${taskCount}.kml`;

      const { id } = await createMultiKMLbulkTask(selectedTaskIds, outputFilename); // Pass filename
      setbulkTaskId(id);
      
      // If the job is already complete (possible in demo/mock implementation)
      if (bulkTaskStatus === 'complete') {
        await downloadResult();
      }
    } catch (error) {
      console.error('Failed to create multi-KML job:', error);
    }
  };

  // Create grid template columns
  const gridTemplateColumns = [
    COLUMN_WIDTHS.checkbox,
    COLUMN_WIDTHS.id,
    COLUMN_WIDTHS.dataset,
    COLUMN_WIDTHS.status,
    COLUMN_WIDTHS.support,
    COLUMN_WIDTHS.createdAt,
    COLUMN_WIDTHS.downloads,
    ...fileTypes.map(() => COLUMN_WIDTHS.fileType),
    COLUMN_WIDTHS.allFiles
  ].join(' ');

  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      height: 'calc(100vh - 64px - 48px - 280px)', // viewport - header - padding - content above table
      minHeight: '400px', // Minimum height to ensure usability
      maxHeight: 'calc(100vh - 200px)', // Maximum height with smaller buffer
      border: '1px solid rgba(224, 224, 224, 1)',
      borderRadius: '8px',
      backgroundColor: 'white',
      overflow: 'hidden'
    }}>
      {/* Fixed Bulk Actions Toolbar */}
      <div style={{ flexShrink: 0, borderBottom: '1px solid rgba(224, 224, 224, 1)' }}>
        <BulkActionsToolbar
          selectedIds={selectedTaskIds}
          downloadingFiles={downloadingFiles}
          onDownloadSelected={handleDownloadSelected}
          bulkTaskStatus={bulkTaskStatus}
          onCreateMultiKML={handleCreateMultiKML}
          bulkTaskError={bulkTaskError}
        />
      </div>

      {/* Fixed Header */}
      <div style={{
        flexShrink: 0,
        borderBottom: '1px solid rgba(224, 224, 224, 1)',
        display: 'grid',
        gridTemplateColumns: gridTemplateColumns,
        gap: 0,
        backgroundColor: '#f5f5f5',
        padding: '8px 0',
        alignItems: 'center'
      }}>
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', padding: '0 8px' }}>
          <Checkbox
            indeterminate={someSelected}
            checked={allSelected}
            onChange={onToggleAllTasksSelection}
            inputProps={{ 'aria-label': 'select all tasks' }}
            size="small"
          />
        </div>
        <div style={{ display: 'flex', alignItems: 'center', padding: '0 8px' }}>
          <TableSortLabel
            active={orderBy === 'id'}
            direction={orderBy === 'id' ? order : 'asc'}
            onClick={() => onSort('id')}
            sx={{
              '& .MuiTableSortLabel-root': { fontSize: '0.875rem', fontWeight: 600 },
              fontSize: '0.875rem',
              fontWeight: 600
            }}
          >
            ID
          </TableSortLabel>
        </div>
        <div style={{ display: 'flex', alignItems: 'center', padding: '0 8px' }}>
          <TableSortLabel
            active={orderBy === 'dataset'}
            direction={orderBy === 'dataset' ? order : 'asc'}
            onClick={() => onSort('dataset')}
            sx={{
              '& .MuiTableSortLabel-root': { fontSize: '0.875rem', fontWeight: 600 },
              fontSize: '0.875rem',
              fontWeight: 600
            }}
          >
            Dataset
          </TableSortLabel>
        </div>
        <div style={{ display: 'flex', alignItems: 'center', padding: '0 8px' }}>
          <TableSortLabel
            active={orderBy === 'status'}
            direction={orderBy === 'status' ? order : 'asc'}
            onClick={() => onSort('status')}
            sx={{
              '& .MuiTableSortLabel-root': { fontSize: '0.875rem', fontWeight: 600 },
              fontSize: '0.875rem',
              fontWeight: 600
            }}
          >
            Status
          </TableSortLabel>
        </div>
        <div style={{ display: 'flex', alignItems: 'center', padding: '0 8px' }}>
          <Typography variant="body2" sx={{ fontSize: '0.875rem', fontWeight: 600, color: 'text.primary' }}>
            Support
          </Typography>
        </div>
        <div style={{ display: 'flex', alignItems: 'center', padding: '0 8px' }}>
          <TableSortLabel
            active={orderBy === 'created_at'}
            direction={orderBy === 'created_at' ? order : 'asc'}
            onClick={() => onSort('created_at')}
            sx={{
              '& .MuiTableSortLabel-root': { fontSize: '0.875rem', fontWeight: 600 },
              fontSize: '0.875rem',
              fontWeight: 600
            }}
          >
            Created At
          </TableSortLabel>
        </div>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end', padding: '0 8px' }}>
          <Typography variant="body2" sx={{ fontSize: '0.875rem', fontWeight: 600, color: 'text.primary' }}>
            Downloads
          </Typography>
        </div>
        {fileTypes.map((type) => (
          <div key={type} style={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center', padding: '0 8px' }}>
            <Typography variant="body2" sx={{ fontSize: '0.875rem', fontWeight: 600, color: 'text.secondary' }}>
              {getFileTypeLabel(type)}
            </Typography>
          </div>
        ))}
        <div style={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center', padding: '0 8px' }}>
          <Typography variant="body2" sx={{ fontSize: '0.875rem', fontWeight: 600, color: 'text.secondary' }}>
            All Files
          </Typography>
        </div>
      </div>

      {/* Scrollable Body */}
      <div style={{
        flexGrow: 1,
        overflow: 'auto',
        backgroundColor: 'white'
      }}>
        {tasks.map((task) => (
          <TaskRow
            key={task.id}
            task={task}
            isSelected={selectedTaskIds.includes(task.id)}
            onToggleSelect={onToggleTaskSelection}
            onDownloadFile={onDownloadFile}
            onDownloadTaskFiles={onDownloadTaskFiles}
            onOpenSupportDialog={(taskId, datasetName) => onOpenSupportDialog(taskId, datasetName, jobId)}
            downloadingFiles={downloadingFiles}
            jobId={jobId}
            gridTemplateColumns={gridTemplateColumns}
          />
        ))}
      </div>

      {/* Fixed Footer */}
      <div style={{
        flexShrink: 0,
        borderTop: '1px solid rgba(224, 224, 224, 1)',
        display: 'grid',
        gridTemplateColumns: gridTemplateColumns,
        gap: 0,
        backgroundColor: '#f9f9f9',
        padding: '8px 0',
        alignItems: 'center'
      }}>
        <div style={{ padding: '0 8px' }}></div>
        <div style={{ padding: '0 8px' }}></div>
        <div style={{ padding: '0 8px' }}></div>
        <div style={{ padding: '0 8px' }}></div>
        <div style={{ padding: '0 8px' }}></div>
        <div style={{ padding: '0 8px' }}></div>
        <div style={{ display: 'flex', alignItems: 'center', padding: '0 8px' }}>
          <Typography variant="body2" color="text.secondary">
            Batch Downloads
          </Typography>
        </div>
        {fileTypes.map((type) => {
          if (!hasFilesOfType(tasks, type)) {
            return <div key={type} style={{ padding: '0 8px' }} />;
          }
          return (
            <div key={type} style={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center', padding: '0 8px' }}>
              <Tooltip title={`Download all ${getFileTypeLabel(type)} files from all tasks`}>
                <span>
                  <IconButton
                    size="medium"
                    onClick={() => onDownloadAllOfType(type)}
                    disabled={downloadingFiles[`type-${type}`]}
                    aria-label={`Download all ${getFileTypeLabel(type)} files`}
                    sx={{
                      color: 'text.secondary',
                      '&:hover': {
                        color: 'primary.main'
                      }
                    }}
                  >
                    {downloadingFiles[`type-${type}`] ? (
                      <CircularProgress size={20} />
                    ) : (
                      <DownloadIcon fileType={type} isMulti={true} />
                    )}
                  </IconButton>
                </span>
              </Tooltip>
            </div>
          );
        })}
        <div style={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center', padding: '0 8px' }}>
          <Tooltip title="Download all files from all tasks">
            <span>
              <IconButton
                size="medium"
                onClick={onDownloadAll}
                disabled={!tasks.some(t => t.task_results && t.task_results.length > 0) || downloadingFiles['all']}
                aria-label="Download all files from all tasks"
                sx={{
                  color: tasks.some(t => t.task_results && t.task_results.length > 0) ? 'text.secondary' : 'action.disabled',
                  '&:hover': {
                    color: tasks.some(t => t.task_results && t.task_results.length > 0) ? 'primary.main' : 'action.disabled'
                  },
                  display: { xs: 'inline-flex', xl: 'none' }
                }}
              >
                {downloadingFiles['all'] ? (
                  <CircularProgress size={20} />
                ) : (
                  <DownloadIcon fileType="all" isMulti={true} />
                )}
              </IconButton>
              <Button
                size="small"
                variant="outlined"
                startIcon={
                  downloadingFiles['all'] ?
                  <CircularProgress size={16} /> :
                  <DownloadIcon fileType="all" isMulti={true} />
                }
                onClick={onDownloadAll}
                disabled={!tasks.some(t => t.task_results && t.task_results.length > 0) || downloadingFiles['all']}
                sx={{
                  fontSize: '0.75rem',
                  fontWeight: 400,
                  textTransform: 'none',
                  borderColor: 'divider',
                  color: tasks.some(t => t.task_results && t.task_results.length > 0) ? 'text.secondary' : 'action.disabled',
                  '&:hover': {
                    borderColor: 'primary.main',
                    backgroundColor: 'action.hover',
                    color: tasks.some(t => t.task_results && t.task_results.length > 0) ? 'primary.main' : 'action.disabled'
                  },
                  '&:disabled': {
                    borderColor: 'divider',
                    color: 'text.disabled'
                  },
                  display: { xs: 'none', xl: 'inline-flex' },
                  '.MuiButton-startIcon': {
                    marginLeft: '4px'
                  }
                }}
              >
                Download All
              </Button>
            </span>
          </Tooltip>
        </div>
      </div>
    </div>
  );
};

export default memo(TasksTable);
